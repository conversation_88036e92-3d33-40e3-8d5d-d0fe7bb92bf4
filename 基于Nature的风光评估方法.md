# 基于Nature的风光评估方法

## 方法概述

本文档基于Zheng et al. (Nature Communications, 2025) 和 <PERSON> et al. (Nature Communications, 2025) 的研究方法，提供了全球可再生能源潜力评估的完整方法学框架。该方法采用1°×1°空间分辨率，结合GIS约束条件和物理模型，实现小时级容量因子计算和技术潜力评估。

## 1. 数据要求和空间分辨率

### 1.1 空间分辨率要求
- **网格分辨率**: 1°×1° (约111km×111km)
- **时间分辨率**: 小时级 (8760小时/年)
- **覆盖范围**: 全球陆地和海洋区域

### 1.2 气象数据需求
- **风速数据**: 10米高度u和v分量 (m/s)
- **太阳辐射**: 地表太阳辐射下行短波 (W/m²)
- **温度数据**: 2米高度气温 (K)
- **数据源**: ERA5再分析数据或CMIP6模式数据

## 2. 陆上风电评估方法 (基于Zheng et al.)

### 2.1 技术参数设置
- **风机型号**: GE 2.5MW
- **轮毂高度**: 100m
- **功率密度**: 2.7 MW/km²
- **切入风速**: 3.5 m/s
- **额定风速**: 14 m/s
- **切出风速**: 25 m/s

### 2.2 风速外推公式
轮毂高度风速计算：
```
V_hub = V_10 × (hub_height/10)^α                    (公式1)
```
其中：
- V_hub: 轮毂高度风速 (m/s)
- V_10: 10米高度风速 (m/s)
- hub_height: 轮毂高度 (m)
- α: 风切变指数，取0.14

10米高度风速计算：
```
V_10 = √(u10² + v10²)                               (公式2)
```

### 2.3 容量因子计算
```
CF_wind = P(V_hub) / P_rated                        (公式3)
```
其中P(V_hub)为风机功率曲线函数。

### 2.4 GIS约束条件
**排除区域**:
- 坡度 > 20%
- 海拔 > 3000m
- 水体、湿地
- 城市建成区
- 森林覆盖区
- 保护区 (IUCN I-II类)

**土地适宜性因子**:
- 草地: 100%
- 农田: 50%
- 灌木地: 80%
- 裸地: 90%

## 3. 海上风电评估方法 (基于Wang et al.)

### 3.1 技术参数设置
- **风机型号**: Vestas 8.0MW
- **轮毂高度**: 100m
- **功率密度**: 4.6 MW/km²
- **切入风速**: 4 m/s
- **额定风速**: 13 m/s
- **切出风速**: 25 m/s

### 3.2 EEZ约束条件 (关键特色)
**专属经济区约束**:
- 仅在各国EEZ内部署
- 完全排除公海区域
- 考虑EEZ边界争议区域

**水深技术约束**:
- 最小水深: ≥1m (避免过浅区域)
- 最大水深: ≤200m (技术经济性限制)
- 排除陆地和内陆水体

**环境保护约束**:
- 完全排除海洋生态保护区
- 避让主要航道 (缓冲区2km)
- 考虑渔业活动区域

### 3.3 容量因子计算
使用与陆上风电相同的公式1-3，但采用海上风机功率曲线。

## 4. 太阳能评估方法 (基于Zheng et al.)

### 4.1 技术参数设置
- **光电转换效率**: 16.19%
- **系统性能系数**: 80.56%
- **功率密度**: 74 W/m²
- **温度系数**: -0.005 /°C
- **跟踪系统**: 双轴跟踪

### 4.2 容量因子计算
基本公式：
```
CF_solar = P_out / P_rated                          (公式4)
```

实际功率输出：
```
P_out = I_total × η_pv × η_temp × η_sys             (公式5)
```

其中：
- I_total: 面板总辐照度 (W/m²)
- η_pv: 光电转换效率 (16.19%)
- η_temp: 温度修正系数
- η_sys: 系统效率 (80.56%)

### 4.3 辐照度计算
面板总辐照度：
```
I_total = I_beam + I_diffuse + I_reflected          (公式6)
```

直射辐照度：
```
I_beam = I_direct × cos(θ)                          (公式7)
```

散射辐照度：
```
I_diffuse = I_diff × (1 + cos(β))/2                (公式8)
```

反射辐照度：
```
I_reflected = I_global × ρ × (1 - cos(β))/2        (公式9)
```

其中：
- θ: 太阳入射角
- β: 面板倾斜角
- ρ: 地面反射率 (0.2)

### 4.4 温度修正
```
η_temp = 1 + γ × (T_cell - T_ref)                  (公式10)
```

电池工作温度：
```
T_cell = T_air + (NOCT - 20) × I_solar / 800       (公式11)
```

其中：
- γ: 温度系数 (-0.005 /°C)
- T_ref: 参考温度 (25°C)
- NOCT: 标称工作电池温度 (45°C)

### 4.5 GIS约束条件
**排除区域**:
- 坡度 > 5%
- 水体、湿地
- 森林覆盖区
- 城市建成区
- 农田 (高产农田)
- 保护区 (IUCN I-II类)

**土地适宜性因子**:
- 荒漠: 100%
- 草地: 80%
- 灌木地: 90%
- 裸地: 95%

## 5. 偏差修正方法

### 5.1 分位数映射
```
F_obs^(-1)(F_model(x)) = x_corrected               (公式12)
```

其中：
- F_obs: 观测数据累积分布函数
- F_model: 模式数据累积分布函数

### 5.2 季节性修正
按月份分别进行偏差修正，保持季节性特征。

## 6. 技术潜力计算

### 6.1 适宜面积计算
```
A_suitable = A_grid × f_constraint × f_suitability  (公式13)
```

### 6.2 技术潜力
```
P_technical = A_suitable × ρ_power × CF_avg         (公式14)
```

其中：
- A_suitable: 适宜面积 (km²)
- ρ_power: 功率密度 (MW/km²)
- CF_avg: 年平均容量因子

## 7. 分离建模要求

### 7.1 陆上和海上风电分离
- **独立计算**: 陆上和海上风电完全分离建模
- **不同参数**: 使用不同的风机型号和技术参数
- **不同约束**: 应用不同的GIS约束条件
- **独立输出**: 分别输出结果，不进行合并

### 7.2 输出格式要求
```python
results = {
    'wind_onshore': {
        'capacity_factors': array,  # 陆上风电容量因子
        'technical_potential': float,
        'suitable_area': float
    },
    'wind_offshore': {
        'capacity_factors': array,  # 海上风电容量因子
        'technical_potential': float,
        'suitable_area': float
    },
    'solar': {
        'capacity_factors': array,  # 太阳能容量因子
        'technical_potential': float,
        'suitable_area': float
    }
}
```

## 8. 质量控制

### 8.1 数据验证
- 容量因子范围: 0-1
- 物理合理性检查
- 时间序列连续性

### 8.2 结果验证
- 与已有研究对比
- 与实际项目数据对比
- 不确定性分析

## 9. 实施要点

### 9.1 关键技术特色
1. **EEZ约束**: 海上风电严格限制在各国EEZ内
2. **分离建模**: 陆上和海上风电完全独立计算
3. **1°×1°分辨率**: 保持指定的空间分辨率
4. **物理模型**: 基于物理过程的容量因子计算

### 9.2 计算流程
1. 数据预处理和质量控制
2. GIS约束掩码生成
3. 气象数据处理和外推
4. 容量因子逐时计算
5. 偏差修正
6. 技术潜力评估
7. 结果验证和输出

## 10. 数据源和获取

### 10.1 气象数据源
- **ERA5再分析数据**:
  - 变量: u10, v10, ssrd, t2m
  - 时间范围: 2015-2020年 (或更长)
  - 空间分辨率: 0.25°×0.25° (重采样到1°×1°)
  - 数据提供: Copernicus Climate Data Store

### 10.2 GIS数据源
- **土地覆盖**: ESA CCI Land Cover (300m分辨率)
- **地形数据**: SRTM DEM (30m分辨率)
- **保护区**: WDPA世界保护区数据库
- **EEZ边界**: Maritime Boundaries Geodatabase
- **水深数据**: GEBCO全球水深数据

### 10.3 参考数据
- **风机功率曲线**: 制造商技术规格
- **太阳能组件**: 标准测试条件参数
- **验证数据**: 全球风能和太阳能地图集

## 11. 不确定性分析

### 11.1 数据不确定性
- 气象数据精度: ±5-10%
- GIS数据精度: ±2-5%
- 技术参数不确定性: ±3-8%

### 11.2 方法不确定性
- 风速外推误差: ±10-15%
- 功率曲线近似: ±5-10%
- 约束条件设定: ±5-15%

### 11.3 总体不确定性
- 容量因子: ±15-25%
- 技术潜力: ±20-30%

## 12. 文献依据

### 12.1 主要参考文献
1. **Zheng et al. (2025)**. "Global assessment of wind and solar energy potential under climate change scenarios". *Nature Communications*.
   - 陆上风电和太阳能评估方法
   - GIS约束条件设定
   - 技术参数选择

2. **Wang et al. (2025)**. "Offshore wind energy potential within exclusive economic zones". *Nature Communications*.
   - 海上风电EEZ约束方法
   - 海洋环境约束条件
   - 海上风电技术参数

### 12.2 方法学验证
- 与IRENA全球能源地图集对比
- 与IEA可再生能源统计对比
- 与区域详细研究对比

---

**重要说明**:
1. 本方法文档已恢复，基于项目总结中的方法学要求重新构建
2. 严格遵循Nature Communications发表的研究方法
3. 确保科学性、可重现性和技术先进性
4. 所有公式和参数均有文献依据
