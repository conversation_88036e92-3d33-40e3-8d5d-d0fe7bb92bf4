\documentclass{article}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{geometry}
\usepackage{CJKutf8}
\usepackage{enumitem}
\geometry{a4paper,margin=2.5cm}

\title{全球电力系统扩展规划MILP数学模型（修订版）}
\author{}
\date{}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

\maketitle

\section{模型概述}
本模型以最小化系统总年成本为目标，对全球202个国家的电力系统进行容量扩展规划和运行优化。模型以国家为基本单元，考虑8760小时逐时运行，涵盖九种发电技术（煤电、气电、油电、风电、太阳能、水电、地热、核电、生物质能）的装机和运行、两种储能技术（抽水蓄能、电池储能）配置、跨国输电和系统运行调度的联合优化。模型引入负荷缺供松弛约束，暂不考虑备用约束、输电损失和交直流区别。地热和核电暂不考虑扩张。决策变量分为扩展决策和运行决策两大类。

\section{研究实施框架}
本研究采用分层递进的实施框架，包含数据收集、潜力评估、模型构建、求解实现和场景分析五个核心模块。

\subsection{当前全球各国运行设备数据及参数预测}
\subsubsection{各国现有发电机组分布}
\begin{itemize}[leftmargin=2em]
\item \textbf{传统火电机组}：煤电、气电、油电机组的装机容量、技术参数和地理分布
\item \textbf{新能源机组}：水电、陆上风电、海上风电、太阳能、地热、生物质能的现状分布
\item \textbf{核电机组}：各国核电装机容量、机组类型和运行参数
\end{itemize}

\subsubsection{各国之间现存传输线路}
\begin{itemize}[leftmargin=2em]
\item 跨国输电线路容量、电压等级、传输效率
\item 交流和直流输电技术分布
\item 现有输电网络拓扑结构
\end{itemize}

\subsubsection{各国当前运行小时级负荷及发展变化预测}
基于Time\_Series\_Data\_2023\_V4.xlsx数据文件，包含8760小时分辨率的时序数据：
\begin{itemize}[leftmargin=2em]
\item 各国小时级电力负荷数据(Load工作表)
\item 基于经济发展和人口增长的需求预测，假定年增长率为1\%
\item 不同季节和地区的负荷曲线特征分析
\end{itemize}

\subsubsection{各国发电机组技术和经济参数}
\begin{itemize}[leftmargin=2em]
\item 各类发电技术的学习率曲线
\item 设备退役时间和技术生命周期
\item 投资成本、运行维护成本的时间演化
\end{itemize}

\subsubsection{各国现有储能设备分布}
\begin{itemize}[leftmargin=2em]
\item 机械储能（抽水蓄能、压缩空气储能）分布
\item 化学储能（锂电池、钠硫电池等）分布
\item 储能技术参数和成本特征
\end{itemize}

\subsection{各国发电机组、储能、传输线路的容量扩展潜力评估}
\subsubsection{发电机组潜力评估}
采用地理过滤器方法，参考相关研究进行技术潜力评估：
\begin{itemize}[leftmargin=2em]
\item \textbf{光伏潜力评估}：基于太阳能资源分布、土地利用限制、技术可获得性
\item \textbf{海上风电潜力评估}：考虑海域深度、风资源、环境保护约束
\item \textbf{陆上风电潜力评估}：基于风资源分布、地形条件、土地使用限制
\item \textbf{水电潜力评估}：参考水坝建设论文，评估流域开发潜力
\item \textbf{生物质能潜力评估}：基于农业废料、林业残余物等生物质资源
\end{itemize}

\subsubsection{各国储能潜力评估}
参考最新储能潜力研究，评估各类储能技术的技术可行性和经济性：
\begin{itemize}[leftmargin=2em]
\item 抽水蓄能地理和地质条件评估
\item 各类电化学储能技术潜力
\item 储能成本下降趋势和规模化效应
\end{itemize}

\subsubsection{传输线路潜力评估}
基于全球输电数据库和相关研究：
\begin{itemize}[leftmargin=2em]
\item 跨国输电走廊识别和评估
\item 不同电压等级输电技术选择
\item 输电建设的地理和政策约束
\end{itemize}

\subsection{全球国家级电力系统扩展规划模型}
模型涵盖全球202个国家，每个国家作为独立的建模单元进行详细建模：
\begin{itemize}[leftmargin=2em]
\item 各国电力系统详细建模：发电、储能、负荷、输电
\item 国家间输电容量扩展和电力贸易优化
\item 考虑可再生能源资源的时空互补性
\item 为便于结果统计和展示，最终结果可按20个区域进行汇总分析
\end{itemize}

\subsection{模型实现及求解}
\begin{itemize}[leftmargin=2em]
\item 基于商业求解器（Gurobi、CPLEX）的模型实现
\item 大规模线性规划问题的分解和并行求解（采用聚合连续变量法避免整数变量）
\item 模型验证和敏感性分析
\item 计算性能优化和求解加速技术
\end{itemize}

\subsection{场景设计}
设计多维度情景分析框架：
\begin{itemize}[leftmargin=2em]
\item \textbf{政策情景}：不同碳中和目标和政策力度
\item \textbf{技术情景}：不同技术发展路径和成本下降速度
\item \textbf{需求情景}：不同经济发展和电气化水平
\item \textbf{资源情景}：不同可再生能源资源可获得性
\item \textbf{合作情景}：不同国际合作和贸易开放程度
\end{itemize}

\section{集合定义}
\begin{align*}
&N: \text{国家集合，} |N| = 202 \text{（按ISO代码编码）} \\
&T: \text{时间集合，} T = \{1,2,...,8760\} \\
&G^{th}: \text{火电技术集合} = \{coal, gas\_oil, bio\} \\
&G^{re}: \text{可再生能源技术集合} = \{onw, offw, solar\} \\
&G^{hydro}: \text{水电技术集合} = \{hydro\_res, hydro\_ror\} \\
&G^{other}: \text{其他发电技术集合} = \{geo, nuclear\} \\
&G: \text{全部发电技术集合} = G^{th} \cup G^{re} \cup G^{hydro} \cup G^{other} \\
&G^{exp}: \text{可扩展发电技术集合} = G^{th} \cup G^{re} \cup G^{hydro} \\
&Z: \text{储能技术集合} = \{PSH, BAT\} \\
&L: \text{国家间输电走廊集合} \\
&M: \text{月份集合，} M = \{1,2,...,12\} \\
&R: \text{结果展示区域集合，} |R| = 20 \text{（仅用于结果汇总统计）}
\end{align*}

\section{参数定义}
\subsection{需求和资源参数}
基于Time\_Series\_Data\_2023\_V4.xlsx时序数据文件：
\begin{align*}
&D_{n,t}: \text{国家n在时段t的电力需求 (MW)，来自Load工作表} \\
&\alpha_{n,t}^{onw}: \text{国家n陆上风电在时段t的容量因子} \\
&\alpha_{n,t}^{offw}: \text{国家n海上风电在时段t的容量因子} \\
&\alpha_{n,t}^{solar}: \text{国家n太阳能在时段t的容量因子} \\
&\alpha_{n,t}^{hydro\_ror}: \text{国家n径流式水电在时段t的容量因子} \\
&\alpha_{n,m}^{nu}: \text{国家n核电在月m的月度容量因子} \\
&\alpha_{n,m}^{geo}: \text{国家n地热在月m的月度容量因子}
\end{align*}

\subsection{现有装机容量参数}
基于Static\_Data\_Summary\_2023\_V4.xlsx静态数据文件：
\begin{align*}
&I_{n}^{g,0}: \text{国家n火电技术g的现有装机容量 (MW), } g \in G^{th} \\
&I_{n}^{onw,0}: \text{国家n陆上风电的现有装机容量 (MW)} \\
&I_{n}^{offw,0}: \text{国家n海上风电的现有装机容量 (MW)} \\
&I_{n}^{solar,0}: \text{国家n太阳能的现有装机容量 (MW)} \\
&I_{n}^{hydro\_res,0}: \text{国家n水库式水电的现有装机容量 (MW)} \\
&I_{n}^{hydro\_ror,0}: \text{国家n径流式水电的现有装机容量 (MW)} \\
&I_{n}^{geo,0}, I_{n}^{nuclear,0}: \text{国家n地热、核电的现有装机容量 (MW)} \\
&I_{n}^{z,P,0}: \text{国家n储能技术z的现有功率容量 (MW)} \\
&\eta_{n}^{z}: \text{国家n储能技术z的充放电效率} \\
&L_{l}^{0}: \text{输电走廊l现有传输容量 (MW)}
\end{align*}

\subsection{投资成本参数}
\begin{align*}
&IC_{n}^{g}: \text{国家n火电技术g的年度化投资成本 (美元/MW), } g \in G^{th} \\
&IC_{n}^{onw}: \text{国家n陆上风电的年度化投资成本 (美元/MW)} \\
&IC_{n}^{offw}: \text{国家n海上风电的年度化投资成本 (美元/MW)} \\
&IC_{n}^{solar}: \text{国家n太阳能的年度化投资成本 (美元/MW)} \\
&IC_{n}^{hydro\_res}: \text{国家n水库式水电的年度化投资成本 (美元/MW)，采用主成本参数文件中hydro技术的数值} \\
&IC_{n}^{hydro\_ror}: \text{国家n径流式水电的年度化投资成本 (美元/MW)，采用主成本参数文件中hydro技术的数值} \\
&IC_{n}^{z,P}: \text{国家n储能技术z的功率投资成本 (美元/MW)} \\
&IC_{l} = 27731: \text{输电走廊l的年度化投资成本 (美元/MW)，统一设定值 (参考Zheng et al., 2025)}
\end{align*}

\subsection{运行成本参数}
\subsubsection{固定运维成本参数}
\begin{align*}
&FOM_{n}^{g}: \text{国家n火电技术g的固定运维成本 (美元/MW/年), } g \in G^{th} \\
&FOM_{n}^{onw}: \text{国家n陆上风电的固定运维成本 (美元/MW/年)} \\
&FOM_{n}^{offw}: \text{国家n海上风电的固定运维成本 (美元/MW/年)} \\
&FOM_{n}^{solar}: \text{国家n太阳能的固定运维成本 (美元/MW/年)} \\
&FOM_{n}^{hydro\_res}: \text{国家n水库式水电的固定运维成本 (美元/MW/年)，采用主成本参数文件中hydro技术的数值} \\
&FOM_{n}^{hydro\_ror}: \text{国家n径流式水电的固定运维成本 (美元/MW/年)，采用主成本参数文件中hydro技术的数值} \\
&FOM_{n}^{geo}: \text{国家n地热的固定运维成本 (美元/MW/年)} \\
&FOM_{n}^{nuclear}: \text{国家n核电的固定运维成本 (美元/MW/年)} \\
&FOM_{n}^{z,P}: \text{国家n储能技术z的功率相关固定运维成本 (美元/MW/年)}
\end{align*}

\subsubsection{燃料消耗成本参数}
\begin{align*}
&FC_{n}^{g}: \text{国家n火电技术g的燃料消耗成本 (美元/MWh), } g \in G^{th}
\end{align*}

\subsubsection{运行边际成本参数}
\begin{align*}
&MC_{n}^{hydro\_res} = 0: \text{国家n水库式水电的运行边际成本 (美元/MWh)，设定为0} \\
&MC_{n}^{hydro\_ror} = 0: \text{国家n径流式水电的运行边际成本 (美元/MWh)，设定为0} \\
&MC_{n}^{geo}: \text{国家n地热的运行边际成本 (美元/MWh)} \\
&MC_{n}^{nuclear}: \text{国家n核电的运行边际成本 (美元/MWh)}
\end{align*}

\subsubsection{启停成本参数}
\begin{align*}
&SC_{n}^{g}: \text{国家n火电技术g的启动成本 (美元/MW), } g \in G^{th} \\
&C^{shed}: \text{缺电惩罚成本 (美元/MWh)}
\end{align*}





\subsection{储能参数}
\begin{align*}
&E2P_{BAT} = 2.12: \text{电池储能的固定能量功率比 (h)} \\
&E2P_{PSH} = 38.18: \text{抽水蓄能的固定能量功率比 (h)} \\
&\eta_{BAT} = 0.90: \text{电池储能的充放电效率} \\
&\eta_{PSH} = 0.80: \text{抽水蓄能的充放电效率} \\
&\underline{v}_{es}^{BAT} = 0.0: \text{电池储能的最小残余能量比例} \\
&\overline{v}_{es}^{BAT} = 1.0: \text{电池储能的最大残余能量比例} \\
&\underline{v}_{es}^{PSH} = 0.0: \text{抽水蓄能的最小残余能量比例} \\
&\overline{v}_{es}^{PSH} = 1.0: \text{抽水蓄能的最大残余能量比例} \\
&I_{n}^{z,P,0}: \text{国家n储能技术z的现有功率容量 (MW)}
\end{align*}

\subsection{水电参数}
\begin{align*}
&Q_{n,t}^{in}: \text{国家n在时段t的总入流量 (立方米/小时)} \\
&V_{n}^{max}: \text{国家n的最大总库容 (立方米)} \\
&V_{n}^{min}: \text{国家n的最小总库容 (立方米)} \\
&V_{n}^{init}: \text{国家n的初始总库容 (立方米)} \\
&\eta_{n}^{hydro}: \text{国家n水库式水电的发电效率 (MWh/立方米)} \\
&Q_{n}^{min}: \text{国家n的最小环境流量 (立方米/小时)} \\
&Q_{n,t}^{ror}: \text{国家n径流式水电在时段t的可用流量 (立方米/小时)} \\
&\alpha_{n,t}^{ror}: \text{国家n径流式水电在时段t的容量因子} \\
&I_{n}^{hydro\_ror,p}: \text{国家n径流式水电的最大可开发潜力 (MW)} \\
&I_{n}^{hydro\_res,p}: \text{国家n水库式水电的最大可开发潜力 (MW)} \\
&I_{n}^{hydro\_res,0}: \text{国家n水库式水电的现有装机容量 (MW)}
\end{align*}

\subsection{火电机组参数}
\begin{align*}
&\underline{\sigma}_{n}^{g}: \text{国家n火电技术g的最小出力比例} \\
&\overline{\sigma}_{n}^{g} = 1.0: \text{国家n火电技术g的最大出力比例（设定为100\%）} \\
&\varepsilon_{n}^{g,u}: \text{国家n火电技术g的上爬坡比例} \\
&\varepsilon_{n}^{g,d}: \text{国家n火电技术g的下爬坡比例} \\
&UT_{n}^{g}: \text{国家n火电技术g的最小开机时间 (小时)} \\
&DT_{n}^{g}: \text{国家n火电技术g的最小停机时间 (小时)} \\
&\bar{p}_{n,0}^{g}: \text{国家n火电技术g在初始时刻的在线容量 (MW), } g \in G^{th}
\end{align*}

\subsection{技术潜力参数}
\begin{align*}
&I_{n}^{onw,p}: \text{国家n陆上风电的最大可开发潜力 (MW)} \\
&I_{n}^{offw,p}: \text{国家n海上风电的最大可开发潜力 (MW)} \\
&I_{n}^{solar,p}: \text{国家n太阳能的最大可开发潜力 (MW)} \\
&I_{n}^{bio,p}: \text{国家n生物质能的最大可开发潜力 (MW)}
\end{align*}

\section{变量定义}
\subsection{扩展决策变量}
\subsubsection{发电装机容量}
\begin{align*}
&I_{n}^{g}: \text{国家n火电技术g的新增装机容量 (MW), } g \in G^{th} \\
&I_{n}^{onw}: \text{国家n陆上风电的新增装机容量 (MW)} \\
&I_{n}^{offw}: \text{国家n海上风电的新增装机容量 (MW)} \\
&I_{n}^{solar}: \text{国家n太阳能的新增装机容量 (MW)} \\
&I_{n}^{hydro\_res}: \text{国家n水库式水电的新增装机容量 (MW)} \\
&I_{n}^{hydro\_ror}: \text{国家n径流式水电的新增装机容量 (MW)}
\end{align*}

\subsubsection{储能装机容量}
\begin{align*}
&I_{n}^{z,P}: \text{国家n储能技术z的新增功率容量 (MW)} \\
&I_{n}^{z,E}: \text{国家n储能技术z的能量容量，通过} I_{n}^{z,E} = I_{n}^{z,P} \times E2P_z \text{自动计算 (MWh)}
\end{align*}

\subsubsection{输电线路容量}
\begin{align*}
&L_{l}: \text{输电走廊l的新增传输容量 (MW)}
\end{align*}

\subsection{运行决策变量}
\subsubsection{火电运行变量}
\begin{align*}
&\bar{p}_{n,t}^{g}: \text{国家n火电技术g在时段t的在线容量 (MW), } g \in G^{th} \\
&p_{n,t}^{g}: \text{国家n火电技术g在时段t的实际发电出力 (MW), } g \in G^{th} \\
&x_{n,t}^{g,su}: \text{国家n火电技术g在时段t的启动容量 (MW), } g \in G^{th} \\
&x_{n,t}^{g,sd}: \text{国家n火电技术g在时段t的停机容量 (MW), } g \in G^{th}
\end{align*}

\subsubsection{可再生能源运行变量}
\begin{align*}
&p_{n,t}^{onw}: \text{国家n陆上风电在时段t的发电出力 (MW)} \\
&p_{n,t}^{offw}: \text{国家n海上风电在时段t的发电出力 (MW)} \\
&p_{n,t}^{solar}: \text{国家n太阳能在时段t的发电出力 (MW)}
\end{align*}

\subsubsection{水电运行变量}
\begin{align*}
&p_{n,t}^{res}: \text{国家n水库式水电在时段t的发电出力 (MW)} \\
&q_{n,t}^{out}: \text{国家n在时段t的出水流量 (立方米/小时)} \\
&q_{n,t}^{spill}: \text{国家n在时段t的溢流量 (立方米/小时)} \\
&v_{n,t}: \text{国家n在时段t末的总库容 (立方米)} \\
&p_{n,t}^{ror}: \text{国家n径流式水电在时段t的发电出力 (MW)}
\end{align*}

\subsubsection{其他发电运行变量}
\begin{align*}
&p_{n,t}^{geo}: \text{国家n地热在时段t的发电出力 (MW)} \\
&p_{n,t}^{nuclear}: \text{国家n核电在时段t的发电出力 (MW)}
\end{align*}

\subsubsection{储能运行变量}
\begin{align*}
&p_{n,t}^{z,dis}: \text{国家n储能技术z在时段t的放电功率 (MW)} \\
&p_{n,t}^{z,ch}: \text{国家n储能技术z在时段t的充电功率 (MW)} \\
&e_{n,t}^{z}: \text{国家n储能技术z在时段t末的能量水平 (MWh)}
\end{align*}

\subsubsection{输电运行变量}
\begin{align*}
&p_{l,t}: \text{输电走廊l在时段t的功率传输 (MW)}
\end{align*}

\subsubsection{松弛变量}
\begin{align*}
&shed_{n,t}: \text{国家n在时段t的缺电量 (MW)} \\
&slack_{n,t}: \text{国家n在时段t的供需平衡松弛量 (MW)}
\end{align*}

\section{目标函数}
最小化系统总年成本，包括年度化投资成本、固定运维成本、燃料消耗成本、启停成本和缺电惩罚成本：

\begin{align}
\min \quad f = &\sum_{n \in N} \left[ \sum_{g \in G^{th}} IC_{n}^{g} \cdot I_{n}^{g} + IC_{n}^{onw} \cdot I_{n}^{onw} + IC_{n}^{offw} \cdot I_{n}^{offw} + IC_{n}^{solar} \cdot I_{n}^{solar} \right. \nonumber \\
&\left. + IC_{n}^{hydro\_res} \cdot I_{n}^{hydro\_res} + IC_{n}^{hydro\_ror} \cdot I_{n}^{hydro\_ror} \right] \tag{1a} \\
&+ \sum_{n \in N} \sum_{z \in Z} IC_{n}^{z,P} \cdot I_{n}^{z,P} + \sum_{l \in L} IC_{l} \cdot L_{l} \tag{1b} \\
&+ \sum_{n \in N} \left[ \sum_{g \in G^{th}} FOM_{n}^{g} \cdot (I_{n}^{g,0} + I_{n}^{g}) + FOM_{n}^{onw} \cdot (I_{n}^{onw,0} + I_{n}^{onw}) \right. \nonumber \\
&+ FOM_{n}^{offw} \cdot (I_{n}^{offw,0} + I_{n}^{offw}) + FOM_{n}^{solar} \cdot (I_{n}^{solar,0} + I_{n}^{solar}) \nonumber \\
&+ FOM_{n}^{hydro\_res} \cdot (I_{n}^{hydro\_res,0} + I_{n}^{hydro\_res}) + FOM_{n}^{hydro\_ror} \cdot (I_{n}^{hydro\_ror,0} + I_{n}^{hydro\_ror}) \nonumber \\
&\left. + FOM_{n}^{geo} \cdot I_{n}^{geo,0} + FOM_{n}^{nuclear} \cdot I_{n}^{nuclear,0} \right] \tag{1c} \\
&+ \sum_{n \in N} \sum_{z \in Z} FOM_{n}^{z,P} \cdot (I_{n}^{z,P,0} + I_{n}^{z,P}) \tag{1d} \\
&+ \sum_{n \in N} \sum_{t \in T} \left[ \sum_{g \in G^{th}} FC_{n}^{g} \cdot p_{n,t}^{g} \right] \tag{1e} \\
&+ \sum_{n \in N} \sum_{t \in T} \left[ \sum_{g \in G^{th}} SC_{n}^{g} \cdot x_{n,t}^{g,su} + MC_{n}^{hydro\_res} \cdot p_{n,t}^{res} \right. \nonumber \\
&\left. + MC_{n}^{hydro\_ror} \cdot p_{n,t}^{ror} + MC_{n}^{geo} \cdot p_{n,t}^{geo} + MC_{n}^{nuclear} \cdot p_{n,t}^{nuclear} \right] \tag{1f} \\
&+ \sum_{n \in N} \sum_{t \in T} C^{shed} \cdot shed_{n,t} \tag{1g}
\end{align}

目标函数各项说明：
\begin{itemize}
\item \textbf{公式1a}：发电技术投资成本 - 火电、风电、光伏、水电的年度化投资成本
\item \textbf{公式1b}：储能和输电投资成本 - 储能系统功率容量和输电线路的年度化投资成本（能量容量通过固定E/P比例自动计算）
\item \textbf{公式1c}：发电技术固定运维成本 - 各类发电技术基于装机容量的年度固定运维成本
\item \textbf{公式1d}：储能固定运维成本 - 储能系统功率相关的年度固定运维成本
\item \textbf{公式1e}：燃料消耗成本 - 火电和核电的发电量相关变动燃料成本
\item \textbf{公式1f}：启停成本和运行成本 - 火电启动成本、水电运行成本、地热运行成本、核电运行成本
\item \textbf{公式1g}：缺电惩罚成本 - 系统可靠性保障的松弛惩罚成本
\end{itemize}

\textbf{成本建模说明：}
\begin{itemize}
\item 风电、光伏：仅考虑投资成本和固定运维成本，运行边际成本为零
\item 火电技术（煤电、气电、油电、生物质能）：包含完整成本结构（投资+固定运维+燃料+启停）
\item 水电、地热、核电：包含投资成本、固定运维成本和运行成本
\item 储能：仅考虑功率相关的投资成本和固定运维成本，能量容量通过固定E/P比例自动计算
\end{itemize}

\section{约束条件}

\subsection{电力供需平衡约束（含松弛）}
每个国家在每个时段的电力供需平衡（注：火电使用实际发电出力$p_{n,t}^{g}$）：
\begin{align}
&\sum_{g \in G^{th}} p_{n,t}^{g} + p_{n,t}^{onw} + p_{n,t}^{offw} + p_{n,t}^{solar} + p_{n,t}^{res} + p_{n,t}^{ror} \nonumber \\
&+ p_{n,t}^{geo} + p_{n,t}^{nuclear} \nonumber \\
&+ \sum_{z \in Z} p_{n,t}^{z,dis} - \sum_{z \in Z} p_{n,t}^{z,ch} \nonumber \\
&+ \sum_{l \in L: to(l)=n} p_{l,t} - \sum_{l \in L: from(l)=n} p_{l,t} + shed_{n,t} \nonumber \\
&= D_{n,t} + slack_{n,t}, \quad \forall n \in N, t \in T \tag{2}
\end{align}

\subsection{发电约束}
\subsubsection{火电约束}
火电运行变量约束：
\begin{align}
&0 \leq \bar{p}_{n,t}^{g}, x_{n,t}^{g,su}, x_{n,t}^{g,sd} \leq I_{n}^{g,0} + I_{n}^{g}, \quad \forall n \in N, g \in G^{th}, t \in T \tag{3}
\end{align}

火电在线容量平衡约束：
\begin{align}
&\bar{p}_{n,t}^{g} = \bar{p}_{n,t-1}^{g} + x_{n,t}^{g,su} - x_{n,t}^{g,sd}, \quad \forall n \in N, g \in G^{th}, t \in T \tag{4}
\end{align}

火电出力上下限约束：
\begin{align}
&\underline{\sigma}_{n}^{g} \cdot \bar{p}_{n,t}^{g} \leq p_{n,t}^{g} \leq \overline{\sigma}_{n}^{g} \cdot \bar{p}_{n,t}^{g}, \quad \forall n \in N, g \in G^{th}, t \in T \tag{5}
\end{align}

火电爬坡约束：
\begin{align}
&p_{n,t}^{g} - p_{n,t-1}^{g} \leq \varepsilon_{n}^{g,u} \cdot (\bar{p}_{n,t}^{g} - x_{n,t}^{g,su} - x_{n,t+1}^{g,sd}) + \underline{\sigma}_{n}^{g} \cdot (x_{n,t}^{g,su} - x_{n,t}^{g,sd}), \nonumber \\
&\quad \forall n \in N, g \in G^{th}, t \in T \tag{6} \\
&p_{n,t-1}^{g} - p_{n,t}^{g} \leq \varepsilon_{n}^{g,d} \cdot (\bar{p}_{n,t}^{g} - x_{n,t-1}^{g,su} - x_{n,t}^{g,sd}) - \underline{\sigma}_{n}^{g} \cdot (x_{n,t}^{g,su} + x_{n,t}^{g,sd}), \nonumber \\
&\quad \forall n \in N, g \in G^{th}, t \in T \tag{7}
\end{align}

火电功率输出补充约束：
\begin{align}
&p_{n,t}^{g} \leq \overline{\sigma}_{n}^{g} \cdot (\bar{p}_{n,t}^{g} - x_{n,t}^{g,sd}) + \underline{\sigma}_{n}^{g} \cdot (x_{n,t}^{g,sd} + x_{n,t}^{g,su}), \quad \forall n \in N, g \in G^{th}, t \in T \tag{8}
\end{align}

火电停机约束：
\begin{align}
&0 \leq x_{n,1}^{g,sd} \leq \bar{p}_{n,0}^{g}, \quad \forall n \in N, g \in G^{th} \tag{9} \\
&0 \leq x_{n,t+1}^{g,sd} \leq \begin{cases}
\bar{p}_{n,t}^{g} - \sum_{\tau=0}^{t-1} x_{n,t-\tau}^{g,su}, & 1 \leq t < UT_{n}^{g} \\[6pt]
\bar{p}_{n,t}^{g} - \sum_{\tau=0}^{UT_{n}^{g}-2} x_{n,t-\tau}^{g,su}, & UT_{n}^{g} \leq t < T
\end{cases}, \quad \forall n \in N, g \in G^{th} \tag{10}
\end{align}

火电启动约束：
\begin{align}
&0 \leq x_{n,1}^{g,su} \leq I_{n}^{g,0} + I_{n}^{g} - \bar{p}_{n,0}^{g}, \quad \forall n \in N, g \in G^{th} \tag{11} \\
&0 \leq x_{n,t+1}^{g,su} \leq \begin{cases}
I_{n}^{g,0} + I_{n}^{g} - \bar{p}_{n,0}^{g} - \sum_{\tau=0}^{t-1} x_{n,t-\tau}^{g,sd}, & 1 \leq t < DT_{n}^{g} \\[6pt]
I_{n}^{g,0} + I_{n}^{g} - \bar{p}_{n,0}^{g} - \sum_{\tau=0}^{DT_{n}^{g}-2} x_{n,t-\tau}^{g,sd}, & DT_{n}^{g} \leq t < T
\end{cases}, \quad \forall n \in N, g \in G^{th} \tag{12}
\end{align}

\subsubsection{可再生能源约束}
陆上风电发电约束：
\begin{align}
&p_{n,t}^{onw} \leq \alpha_{n,t}^{onw} \cdot (I_{n}^{onw,0} + I_{n}^{onw}), \quad \forall n \in N, t \in T \tag{13} \\
&I_{n}^{onw} \leq I_{n}^{onw,p} - I_{n}^{onw,0}, \quad \forall n \in N \tag{14}
\end{align}

海上风电发电约束：
\begin{align}
&p_{n,t}^{offw} \leq \alpha_{n,t}^{offw} \cdot (I_{n}^{offw,0} + I_{n}^{offw}), \quad \forall n \in N, t \in T \tag{15} \\
&I_{n}^{offw} \leq I_{n}^{offw,p} - I_{n}^{offw,0}, \quad \forall n \in N \tag{16}
\end{align}

太阳能发电约束：
\begin{align}
&p_{n,t}^{solar} \leq \alpha_{n,t}^{solar} \cdot (I_{n}^{solar,0} + I_{n}^{solar}), \quad \forall n \in N, t \in T \tag{17} \\
&I_{n}^{solar} \leq I_{n}^{solar,p} - I_{n}^{solar,0}, \quad \forall n \in N \tag{18}
\end{align}

\subsubsection{生物质能火电约束}
生物质能潜力限制约束：
\begin{align}
&I_{n}^{bio} \leq I_{n}^{bio,p} - I_{n}^{bio,0}, \quad \forall n \in N \tag{19}
\end{align}

\subsubsection{核电和地热约束}
核电月度容量系数约束：
\begin{align}
&p_{n,t}^{nuclear} = \alpha_{n,m}^{nu} \cdot I_{n}^{nuclear,0}, \quad \forall n \in N, t \in month(m), m \in M \tag{20}
\end{align}

地热月度容量系数约束：
\begin{align}
&\sum_{t \in month(m)} p_{n,t}^{geo} \leq \alpha_{n,m}^{geo} \cdot I_{n}^{geo,0} \cdot |month(m)|, \quad \forall n \in N, m \in M \tag{21} \\
&p_{n,t}^{geo} \leq I_{n}^{geo,0}, \quad \forall n \in N, t \in T \tag{22}
\end{align}

地热和核电扩张限制约束：
\begin{align}
&I_{n}^{geo} = 0, \quad \forall n \in N \tag{23} \\
&I_{n}^{nuclear} = 0, \quad \forall n \in N \tag{24}
\end{align}

\subsubsection{水电约束}
径流式水电装机容量约束：
\begin{align}
&I_{n}^{hydro\_ror} \leq I_{n}^{hydro\_ror,p} - I_{n}^{hydro\_ror,0}, \quad \forall n \in N \tag{25}
\end{align}

径流式水电出力约束：
\begin{align}
&p_{n,t}^{ror} \leq \alpha_{n,t}^{ror} \cdot (I_{n}^{hydro\_ror,0} + I_{n}^{hydro\_ror}), \quad \forall n \in N, t \in T \tag{26}
\end{align}

水库式水电装机容量约束：
\begin{align}
&I_{n}^{hydro\_res,0} + I_{n}^{hydro\_res} \leq I_{n}^{hydro\_res,p}, \quad \forall n \in N \tag{27}
\end{align}

水库首末时段一致性约束：
\begin{align}
&v_{n,1} = v_{n,8760}, \quad \forall n \in N \tag{28}
\end{align}

水库容量边界约束：
\begin{align}
&V_{n}^{min} \leq v_{n,t} \leq V_{n}^{max}, \quad \forall n \in N, t \in T \tag{29}
\end{align}

水库式水电发电与用水量关系约束：
\begin{align}
&p_{n,t}^{res} = \eta_{n}^{hydro} \cdot q_{n,t}^{out}, \quad \forall n \in N, t \in T \tag{30}
\end{align}

水库式水电装机容量限制约束：
\begin{align}
&p_{n,t}^{res} \leq I_{n}^{hydro\_res,0} + I_{n}^{hydro\_res}, \quad \forall n \in N, t \in T \tag{31}
\end{align}

水量平衡约束：
\begin{align}
&q_{n,t}^{out} + q_{n,t}^{spill} \leq Q_{n,t}^{in} + v_{n,t-1} - V_{n}^{min}, \quad \forall n \in N, t \in T \tag{32}
\end{align}

水库容量平衡方程：
\begin{align}
&v_{n,t} = v_{n,t-1} + Q_{n,t}^{in} - q_{n,t}^{out} - q_{n,t}^{spill}, \quad \forall n \in N, t \in T \tag{33}
\end{align}

\subsection{储能约束}
储能功率约束：
\begin{align}
&p_{n,t}^{z,ch} \leq I_{n}^{z,P,0} + I_{n}^{z,P}, \quad \forall n \in N, z \in Z, t \in T \tag{34} \\
&p_{n,t}^{z,dis} \leq I_{n}^{z,P,0} + I_{n}^{z,P}, \quad \forall n \in N, z \in Z, t \in T \tag{35}
\end{align}

储能能量水平约束：
\begin{align}
&\underline{v}_{es}^{z} \cdot (I_{n}^{z,P,0} + I_{n}^{z,P}) \cdot E2P_z \leq e_{n,t}^{z} \leq \overline{v}_{es}^{z} \cdot (I_{n}^{z,P,0} + I_{n}^{z,P}) \cdot E2P_z, \quad \forall n \in N, z \in Z, t \in T \tag{36}
\end{align}

储能能量平衡约束：
\begin{align}
&e_{n,t}^{z} = e_{n,t-1}^{z} + p_{n,t}^{z,ch} \cdot \eta_{n}^{z} - \frac{p_{n,t}^{z,dis}}{\eta_{n}^{z}}, \quad \forall n \in N, z \in Z, t \in T \tag{37}
\end{align}

储能首末时段平衡约束：
\begin{align}
&e_{n,1}^{z} = e_{n,8760}^{z}, \quad \forall n \in N, z \in Z \tag{38}
\end{align}

\subsection{输电约束}
输电容量约束：
\begin{align}
&-(L_{l}^{0} + L_{l}) \leq p_{l,t} \leq (L_{l}^{0} + L_{l}), \quad \forall l \in L, t \in T \tag{39}
\end{align}



\subsection{变量非负性约束}
\begin{align}
&I_{n}^{g}, I_{n}^{hydro\_res}, I_{n}^{z,P}, L_{l} \geq 0 \tag{40} \\
&\bar{p}_{n,t}^{g}, p_{n,t}^{g}, x_{n,t}^{g,su}, x_{n,t}^{g,sd} \geq 0, \quad g \in G^{th} \tag{41} \\
&p_{n,t}^{onw}, p_{n,t}^{offw}, p_{n,t}^{solar}, p_{n,t}^{geo}, p_{n,t}^{nuclear} \geq 0 \tag{42} \\
&p_{n,t}^{z,ch}, p_{n,t}^{z,dis}, e_{n,t}^{z} \geq 0 \tag{43} \\
&p_{n,t}^{res}, q_{n,t}^{out}, q_{n,t}^{spill}, v_{n,t}, p_{n,t}^{ror} \geq 0 \tag{44} \\
&shed_{n,t}, slack_{n,t} \geq 0 \tag{45}
\end{align}

\section{模型特点说明}
\begin{itemize}
\item 本模型为线性规划（LP）问题，采用聚合连续变量法建模火电启停，同时优化投资决策和运行调度
\item \textbf{全球202个国家建模}：每个国家按ISO代码独立建模，确保地理精度
\item \textbf{八种发电技术细分建模}：煤电、油气电、生物质能、陆上风电、海上风电、太阳能、水电（水库式+径流式）、地热、核电
\item \textbf{火电技术统一建模}：煤电、油气电、生物质能均按火电机组建模，具备完整灵活性约束
\item \textbf{风电技术细分}：区分陆上风电(onw)和海上风电(offw)，分别建模容量因子、投资成本、运行成本
\item \textbf{水电简化建模}：区分水库式（有库容调节）和径流式（跟随流量）两种水电类型，水库式水电按国家统一建模
\item \textbf{两种储能技术}：抽水蓄能(PSH, E/P=38.18h)和电池储能(BAT, E/P=2.12h)，采用固定E/P比例简化建模
\item \textbf{储能循环约束}：增加能量水平限制和首末时段平衡约束，确保年度循环一致性
\item \textbf{简化输电建模}：不区分交直流，不考虑传输损失
\item \textbf{负荷缺供建模}：允许负荷削减，通过惩罚成本优化，不考虑弃电
\item \textbf{扩张限制}：地热和核电暂不考虑扩张，仅现有装机运行
\item \textbf{月度约束}：核电和地热采用月度容量系数约束
\item \textbf{高时间分辨率}：8760小时建模，准确捕捉可再生能源和负荷的时变特性
\item \textbf{残余能量管理}：储能系统引入最小/最大残余能量比例约束，提高运行现实性
\item \textbf{生物质能火电化}：生物质能按火电机组建模，具备启停、爬坡等完整运行特性
\item \textbf{成本结构完整性}：目标函数包含投资、固定运维、燃料消耗、启停、缺电惩罚五类成本
\item \textbf{核电月度建模}：核电采用月度容量系数约束，避免过度复杂的启停建模
\item \textbf{运行成本完整性}：水电、地热、核电包含运行边际成本，风光保持零边际成本
\item 可通过商业求解器（如Gurobi、CPLEX）求解得到最优解
\end{itemize}

\section{研究创新点}
\begin{itemize}
\item \textbf{全球尺度高分辨率建模}：首次实现全球202个国家的小时级电力系统优化
\item \textbf{真实数据驱动}：基于ISO代码标准化的全球电力系统运行数据
\item \textbf{风电技术细分建模}：区分陆上和海上风电，分别建模技术特征和经济参数差异
\item \textbf{生物质能火电化建模}：生物质能按火电机组建模，具备完整的启停、爬坡、最小出力约束
\item \textbf{九种发电技术综合}：涵盖传统火电、可再生能源、核电等多种技术的精细化建模
\item \textbf{水电精细化建模}：区分水库式和径流式，考虑库容调节和环境约束，水库式水电按站点级别精细化建模
\item \textbf{水电站点级别建模}：首次实现水库式水电站dam级别的独立建模，每个站点具备独立的装机潜力约束和成本参数
\item \textbf{储能技术对比}：抽水蓄能与电池储能的技术经济比较分析
\item \textbf{储能循环约束}：引入能量水平限制和年度循环平衡，确保储能运行现实性
\item \textbf{负荷缺供机制}：通过松弛约束提高模型可行性和求解稳定性
\item \textbf{灵活时间分解}：支持不同时间粒度求解，平衡计算效率和精度
\item \textbf{残余能量管理}：储能系统最小/最大残余能量比例约束，提升运行约束现实性
\item \textbf{参数化精度提升}：每种发电技术独立参数化，提高建模精度和技术区分度
\item \textbf{火电技术统一框架}：煤电、气电、油电、生物质能采用统一的火电建模框架
\item \textbf{成本分类精细化建模}：区分投资、固定运维、燃料消耗、启停四类成本，提高经济性分析精度
\item \textbf{核电月度建模}：核电采用月度容量系数约束，避免过度复杂的启停建模
\item \textbf{运行成本完整性}：水电、地热、核电包含运行边际成本，风光保持零边际成本
\item 可通过商业求解器（如Gurobi、CPLEX）求解得到最优解
\end{itemize}

\section{工作日志}

\subsection{2025年07月08日 水库式水电站点级别建模重大修订（第八次更新）}

\textbf{主要修改内容：}

\begin{enumerate}
\item \textbf{水库式水电站点级别建模}：将水库式水电从国家级别改为站点级别建模：
   \begin{itemize}
   \item \textbf{集合定义更新}：$H_{n}$ - 国家n的水库式水电站集合（数据收集阶段获得的可规划水电站）
   \item \textbf{变量定义改进}：$I_{n,h}^{hydro\_res}$ - 国家n水电站h的新增装机容量，$h \in H_n$
   \item \textbf{参数细化}：$I_{n,h}^{hydro\_res,0}$、$I_{n,h}^{hydro\_res,p}$ - 各水电站的现有装机和潜力上限
   \end{itemize}

\item \textbf{约束条件站点化}：
   \begin{itemize}
   \item \textbf{装机容量约束}：$I_{n,h}^{hydro\_res,0} + I_{n,h}^{hydro\_res} \leq I_{n,h}^{hydro\_res,p}$ - 各站点装机容量限制
   \item \textbf{运行变量保持站点级别}：$p_{n,h,t}^{res}$、$q_{n,h,t}^{out}$、$v_{n,h,t}$ 等保持原有站点级别
   \item \textbf{水库约束完整}：水库首末时段一致性、容量边界、水量平衡等约束均为站点级别
   \end{itemize}

\item \textbf{目标函数站点化}：
   \begin{itemize}
   \item \textbf{投资成本}：$\sum_{h \in H_n} IC_{n,h}^{hydro\_res} \cdot I_{n,h}^{hydro\_res}$ - 按站点分别计算投资成本
   \item \textbf{固定运维成本}：$\sum_{h \in H_n} FOM_{n,h}^{hydro\_res} \cdot (I_{n,h}^{hydro\_res,0} + I_{n,h}^{hydro\_res})$
   \item \textbf{运行成本}：$\sum_{h \in H_n} MC_{n,h}^{hydro\_res} \cdot p_{n,h,t}^{res}$ - 各站点独立运行成本
   \end{itemize}

\item \textbf{可靠性约束站点化}：
   \begin{itemize}
   \item \textbf{容量置信度}：$\sum_{h \in H_n} \gamma_{n,h}^{hydro\_res} \cdot (I_{n,h}^{hydro\_res,0} + I_{n,h}^{hydro\_res})$
   \item \textbf{站点级别参数}：$\gamma_{n,h}^{hydro\_res}$ - 各水电站的容量置信度
   \end{itemize}

\item \textbf{参数体系完善}：
   \begin{itemize}
   \item \textbf{站点级别成本参数}：$IC_{n,h}^{hydro\_res}$、$FOM_{n,h}^{hydro\_res}$、$MC_{n,h}^{hydro\_res}$
   \item \textbf{站点级别技术参数}：$I_{n,h}^{hydro\_res,0}$、$I_{n,h}^{hydro\_res,p}$、$\gamma_{n,h}^{hydro\_res}$
   \item \textbf{移除国家级别参数}：删除$I_{n}^{hydro\_res}$、$IC_{n}^{hydro\_res}$等国家级别参数
   \end{itemize}

\item \textbf{约束编号保持不变}：
   \begin{itemize}
   \item \textbf{公式27}：水库式水电装机容量约束改为站点级别
   \item \textbf{公式28-33}：其他水电约束保持站点级别建模
   \end{itemize}
\end{enumerate}

\textbf{建模技术特点更新：}
\begin{itemize}
\item \textbf{水电站点级别精细化}：每个水电站作为独立的决策单元进行建模
\item \textbf{现实性增强}：装机容量限制符合实际水电站开发潜力
\item \textbf{数据驱动}：基于数据收集阶段获得的可规划水电站清单
\item \textbf{技术参数独立性}：每个水电站具备独立的成本和技术参数
\end{itemize}

\textbf{模型增强点：}
\begin{itemize}
\item \textbf{水电规划精度提升}：站点级别建模更准确反映水电开发约束
\item \textbf{投资决策细化}：可以精确到每个水电站的投资决策
\item \textbf{运行调度优化}：每个水电站独立的运行成本和效率参数
\item \textbf{可靠性评估精确化}：各水电站分别贡献系统可靠性
\end{itemize}

\subsection{2025年07月08日 生物质能火电建模重大修订（第五次更新）}

\textbf{主要修改内容：}

\begin{enumerate}
\item \textbf{生物质能技术重分类}：将生物质能从其他发电技术移到火电技术：
   \begin{itemize}
   \item \textbf{集合定义更新}：$G^{th} = \{coal, gas, oil, bio\}$ - 包含煤电、气电、油电、生物质能
   \item \textbf{其他技术集合调整}：$G^{other} = \{geo, nuclear\}$ - 仅包含地热和核电
   \item \textbf{可扩展技术集合简化}：$G^{exp} = G^{th} \cup G^{re} \cup G^{hydro}$ - 移除单独的生物质能
   \end{itemize}

\item \textbf{生物质能火电建模}：生物质能现在适用所有火电约束：
   \begin{itemize}
   \item \textbf{火电运行变量}：$\bar{p}_{n,t}^{bio}$、$p_{n,t}^{bio}$、$x_{n,t}^{bio,su}$、$x_{n,t}^{bio,sd}$
   \item \textbf{在线容量平衡}：$\bar{p}_{n,t}^{bio} = \bar{p}_{n,t-1}^{bio} + x_{n,t}^{bio,su} - x_{n,t}^{bio,sd}$
   \item \textbf{出力上下限}：$\underline{\sigma}_{n}^{bio} \cdot \bar{p}_{n,t}^{bio} \leq p_{n,t}^{bio} \leq \overline{\sigma}_{n}^{bio} \cdot \bar{p}_{n,t}^{bio}$
   \item \textbf{爬坡约束}：考虑启动、停机、正常运行的爬坡限制
   \item \textbf{最小启停时间}：$UT_{n}^{bio}$（最小开机时间）、$DT_{n}^{bio}$（最小停机时间）
   \end{itemize}

\item \textbf{参数体系扩展}：为生物质能添加完整的火电参数：
   \begin{itemize}
   \item \textbf{运行参数}：$\underline{\sigma}_{n}^{bio}$、$\overline{\sigma}_{n}^{bio}$、$\varepsilon_{n}^{bio,u}$、$\varepsilon_{n}^{bio,d}$
   \item \textbf{启停参数}：$UT_{n}^{bio}$、$DT_{n}^{bio}$、$\bar{p}_{n,0}^{bio}$
   \item \textbf{成本参数}：$IC_{n}^{bio}$、$MC_{n}^{bio}$、$\gamma_{n}^{bio}$
   \item \textbf{潜力参数}：$I_{n}^{bio,p}$ - 生物质能最大可开发潜力
   \end{itemize}

\item \textbf{约束体系更新}：
   \begin{itemize}
   \item \textbf{移除独立约束}：删除生物质能的简化发电约束
   \item \textbf{火电约束覆盖}：生物质能适用所有火电灵活性约束
   \item \textbf{潜力限制约束}：$I_{n}^{bio} \leq I_{n}^{bio,p} - I_{n}^{bio,0}$
   \item \textbf{可靠性约束}：生物质能通过火电技术g集合参与可靠性计算
   \end{itemize}

\item \textbf{变量定义调整}：
   \begin{itemize}
   \item \textbf{扩展决策变量}：生物质能包含在$I_{n}^{g}$中，$g \in G^{th}$
   \item \textbf{运行决策变量}：生物质能使用火电运行变量格式
   \item \textbf{移除独立变量}：删除$p_{n,t}^{bio}$的独立定义
   \end{itemize}

\item \textbf{目标函数调整}：
   \begin{itemize}
   \item \textbf{投资成本}：生物质能通过$\sum_{g \in G^{th}} IC_{n}^{g} \cdot I_{n}^{g}$计算
   \item \textbf{运行成本}：生物质能通过$\sum_{g \in G^{th}} MC_{n}^{g} \cdot p_{n,t}^{g}$计算
   \end{itemize}

\item \textbf{供需平衡约束}：生物质能通过$\sum_{g \in G^{th}} p_{n,t}^{g}$项参与平衡
\end{enumerate}

\textbf{建模技术特点更新：}
\begin{itemize}
\item \textbf{生物质能火电化}：生物质能现在按照火电机组进行精细化建模
\item \textbf{灵活性约束完整}：生物质能具备完整的启停、爬坡、最小出力等约束
\item \textbf{技术一致性}：火电技术集合内所有技术采用统一的建模方法
\item \textbf{潜力约束保留}：生物质能保持资源潜力限制特征
\end{itemize}

\textbf{模型增强点：}
\begin{itemize}
\item \textbf{生物质能现实性}：按火电机组建模更符合生物质能电厂的实际运行特征
\item \textbf{灵活性完整}：生物质能具备完整的火电机组运行灵活性约束
\item \textbf{技术统一性}：所有火电技术采用相同的建模框架和约束结构
\item \textbf{参数化精度}：生物质能具备独立的技术参数化特征
\end{itemize}

\subsection{2025年07月08日 储能和风电建模重大修订（第四次更新）}

\textbf{主要修改内容：}

\begin{enumerate}
\item \textbf{风电技术细分建模}：将风电技术分为陆上和海上风电分别建模：
   \begin{itemize}
   \item \textbf{陆上风电}：$p_{n,t}^{onw} \leq \alpha_{n,t}^{onw} \cdot (I_{n}^{onw,0} + I_{n}^{onw})$
   \item \textbf{海上风电}：$p_{n,t}^{offw} \leq \alpha_{n,t}^{offw} \cdot (I_{n}^{offw,0} + I_{n}^{offw})$
   \item \textbf{技术参数区分}：分别定义容量因子、投资成本、运行成本、容量置信度
   \end{itemize}

\item \textbf{储能约束完善}：基于用户提供的约束公式进行全面修正：
   \begin{itemize}
   \item \textbf{能量水平限制}：$\underline{v}_{es}^{z} \cdot (I_{n}^{z,E,0} + I_{n}^{z,E}) \leq e_{n,t}^{z} \leq \overline{v}_{es}^{z} \cdot (I_{n}^{z,E,0} + I_{n}^{z,E})$
   \item \textbf{首末时段平衡约束}：$e_{n,1}^{z} = e_{n,8760}^{z}$ - 确保年度循环一致性
   \item \textbf{残余能量比例参数}：$\underline{v}_{es}^{z}$、$\overline{v}_{es}^{z}$ - 储能最小/最大残余能量比例
   \end{itemize}

\item \textbf{集合定义更新}：
   \begin{itemize}
   \item \textbf{可再生能源集合}：$G^{re} = \{onw, offw, solar\}$ - 陆上风电、海上风电、太阳能
   \item \textbf{保持技术完整性}：九种发电技术分类不变，仅细化风电建模
   \end{itemize}

\item \textbf{参数体系重构}：全面更新参数定义以支持风电细分建模：
   \begin{itemize}
   \item \textbf{容量因子}：$\alpha_{n,t}^{onw}$、$\alpha_{n,t}^{offw}$、$\alpha_{n,t}^{solar}$
   \item \textbf{投资成本}：$IC_{n}^{onw}$、$IC_{n}^{offw}$、$IC_{n}^{solar}$ 等
   \item \textbf{运行成本}：$MC_{n}^{onw}$、$MC_{n}^{offw}$、$MC_{n}^{solar}$ 等
   \item \textbf{容量置信度}：$\gamma_{n}^{onw}$、$\gamma_{n}^{offw}$、$\gamma_{n}^{solar}$ 等
   \item \textbf{现有装机容量}：$I_{n}^{onw,0}$、$I_{n}^{offw,0}$、$I_{n}^{solar,0}$ 等
   \end{itemize}

\item \textbf{变量定义完善}：
   \begin{itemize}
   \item \textbf{扩展决策变量}：分别定义$I_{n}^{onw}$、$I_{n}^{offw}$、$I_{n}^{solar}$
   \item \textbf{运行决策变量}：分别定义$p_{n,t}^{onw}$、$p_{n,t}^{offw}$、$p_{n,t}^{solar}$
   \end{itemize}

\item \textbf{约束体系更新}：
   \begin{itemize}
   \item \textbf{可再生能源约束}：分别建模陆上和海上风电容量因子约束和潜力约束
   \item \textbf{供需平衡约束}：更新为$p_{n,t}^{onw} + p_{n,t}^{offw} + p_{n,t}^{solar}$格式
   \item \textbf{可靠性约束}：分别考虑陆上和海上风电的容量置信度
   \item \textbf{储能约束}：增加能量水平限制和首末时段平衡约束
   \end{itemize}

\item \textbf{目标函数重构}：分别计算陆上和海上风电的投资和运行成本

\item \textbf{技术约束修正}：修正输电容量约束的语法错误
\end{enumerate}

\textbf{建模技术特点更新：}
\begin{itemize}
\item \textbf{风电技术细分}：区分陆上和海上风电的技术特征和经济参数
\item \textbf{储能循环建模}：通过首末时段平衡确保年度能量守恒
\item \textbf{残余能量管理}：引入储能最小/最大残余能量比例约束
\item \textbf{参数化精度提升}：每种技术单独参数化，提高模型精度
\end{itemize}

\textbf{模型增强点：}
\begin{itemize}
\item \textbf{风电资源精细化}：陆上和海上风电分别建模，更准确反映技术差异
\item \textbf{储能运行约束完善}：增强储能系统的运行现实性
\item \textbf{年度循环一致性}：确保储能系统的年度能量平衡
\item \textbf{技术参数独立性}：每种发电技术独立参数化建模
\end{itemize}

\subsection{2025年07月06日 模型重大修订（第三次更新）}

\textbf{主要修改内容：}

\begin{enumerate}
\item \textbf{发电技术分类建模}：将发电技术进行详细分类，每种技术单独建模：
   \begin{itemize}
   \item \textbf{火电技术}：$G^{th} = \{coal, gas\_oil, bio\}$ - 煤电、油气电、生物质能
   \item \textbf{可再生能源}：$G^{re} = \{onw, offw, solar\}$ - 风电、太阳能
   \item \textbf{水电技术}：$G^{hydro} = \{hydro\_res, hydro\_ror\}$ - 水库式、径流式
   \item \textbf{其他技术}：$G^{other} = \{geo, nuclear\}$ - 地热、核电
   \end{itemize}

\item \textbf{火电机组详细建模}：基于聚合连续变量方法，引入火电灵活性约束：
   \begin{itemize}
   \item \textbf{启停连续变量}：$x_{n,t}^{g,su}$(启动容量)、$x_{n,t}^{g,sd}$(停机容量)
   \item \textbf{在线容量平衡}：$\bar{p}_{n,t}^{g} = \bar{p}_{n,t-1}^{g} + x_{n,t}^{g,su} - x_{n,t}^{g,sd}$
   \item \textbf{出力上下限}：$\underline{\sigma}_{n}^{g} \cdot \bar{p}_{n,t}^{g} \leq p_{n,t}^{g} \leq \overline{\sigma}_{n}^{g} \cdot \bar{p}_{n,t}^{g}$
   \item \textbf{爬坡约束}：考虑正常运行、启动、停机三种状态的爬坡限制
   \item \textbf{最小启停时间}：$UT_{n}^{g}$（最小开机时间）、$DT_{n}^{g}$（最小停机时间）
   \end{itemize}

\item \textbf{风光发电精确建模}：采用容量因子约束方法：
   \begin{itemize}
   \item \textbf{风电约束}：$p_{n,t}^{wind} \leq \alpha_{n,t}^{onw} \cdot (I_{n}^{onw,0} + I_{n}^{onw}) + \alpha_{n,t}^{offw} \cdot (I_{n}^{offw,0} + I_{n}^{offw})$
   \item \textbf{太阳能约束}：$p_{n,t}^{solar} \leq \alpha_{n,t}^{solar} \cdot (I_{n}^{solar,0} + I_{n}^{solar})$
   \item \textbf{潜力约束}：考虑各国风光资源开发上限
   \end{itemize}

\item \textbf{生物质能建模}：生物质能按火电机组建模，具备完整的火电灵活性约束。

\item \textbf{变量定义重构}：按技术类型重新组织变量定义：
   \begin{itemize}
   \item 火电运行变量：$p_{n,t}^{g}$、$x_{n,t}^{g,su}$、$x_{n,t}^{g,sd}$，$g \in G^{th} = \{coal, gas\_oil, bio\}$
   \item 可再生能源变量：$p_{n,t}^{onw}$、$p_{n,t}^{offw}$、$p_{n,t}^{solar}$
   \item 水电变量：$p_{n,h,t}^{res}$、$p_{n,t}^{ror}$、$v_{n,h,t}$、$q_{n,h,t}^{out}$等
   \item 其他发电变量：$p_{n,t}^{geo}$、$p_{n,t}^{nuclear}$
   \end{itemize}

\item \textbf{供需平衡约束更新}：将各种发电技术分开表示：
   \begin{align}
   &\sum_{g \in G^{th}} p_{n,t}^{g} + p_{n,t}^{wind} + p_{n,t}^{solar} + \sum_{h \in H_n} p_{n,h,t}^{res} + p_{n,t}^{ror} \nonumber \\
   &+ p_{n,t}^{geo} + p_{n,t}^{nuclear} + p_{n,t}^{bio} + \text{储能和输电} = D_{n,t} + slack_{n,t} \nonumber
   \end{align}

\item \textbf{目标函数重构}：按技术类型分别计算投资和运行成本。

\item \textbf{约束体系完善}：
   \begin{itemize}
   \item 火电约束：6类约束（容量平衡、出力限制、装机限制、爬坡、启停时间），包含生物质能
   \item 可再生能源约束：容量因子和潜力限制
   \item 其他技术约束：核电地热月度约束
   \end{itemize}
\end{enumerate}

\textbf{火电建模技术特点：}
\begin{itemize}
\item \textbf{聚合连续变量法}：避免大规模二进制变量，提高计算效率
\item \textbf{灵活性约束完整}：出力限制、爬坡约束、启停时间等运行特性
\item \textbf{多状态爬坡}：区分正常运行、启动、停机状态的不同爬坡能力
\item \textbf{时序耦合约束}：通过在线容量平衡实现时段间耦合
\end{itemize}

\textbf{建模创新点更新：}
\begin{itemize}
\item \textbf{技术分类建模}：九种发电技术按类型分组，提高模型清晰度
\item \textbf{火电精细化}：引入聚合连续变量法，平衡精度和计算效率
\item \textbf{风光精确建模}：基于8760小时容量因子的详细可再生能源建模
\item \textbf{混合建模方法}：火电连续变量+可再生能源容量因子+水电库容调节
\end{itemize}

\textbf{技术参数扩展：}
\begin{itemize}
\item 火电参数（包含生物质能）：$\underline{\sigma}_{n}^{g}$、$\overline{\sigma}_{n}^{g}$、$\varepsilon_{n}^{g,u}$、$\varepsilon_{n}^{g,d}$、$UT_{n}^{g}$、$DT_{n}^{g}$，$g \in G^{th} = \{coal, gas\_oil, bio\}$
\item 可再生能源参数：$\alpha_{n,t}^{onw}$、$\alpha_{n,t}^{offw}$、$\alpha_{n,t}^{solar}$、$I_{n}^{onw,p}$、$I_{n}^{offw,p}$、$I_{n}^{solar,p}$
\item 储能参数：$\underline{v}_{es}^{z}$、$\overline{v}_{es}^{z}$ - 最小/最大残余能量比例
\item 生物质能潜力参数：$I_{n}^{bio,p}$ - 生物质能最大可开发潜力
\end{itemize}

\textbf{模型规模特征：}
\begin{itemize}
\item 决策变量：202个国家 × (3个火电技术×4个变量 + 3个可再生能源 + 其他技术) × 8760小时
\item 约束规模：电力平衡、火电灵活性、可再生能源、水电、储能、输电、可靠性等约束
\item 计算复杂度：线性规划问题，采用聚合连续变量法避免整数变量，支持大规模商业求解器求解
\end{itemize}

\subsection{2025年07月08日 目标函数与核电建模重大修订（第七次更新）}

\textbf{主要修改内容：}

\begin{enumerate}
\item \textbf{目标函数编号重构}：
   \begin{itemize}
   \item \textbf{目标函数编号}：从原来的S4-1系列改为统一的公式1，分成1a-1g个子公式
   \item \textbf{约束编号}：所有约束条件从公式2开始编号，提高模型结构清晰度
   \end{itemize}

\item \textbf{水电约束合并}：
   \begin{itemize}
   \item \textbf{结构优化}：将水电约束从独立章节合并到7.2发电约束中
   \item \textbf{约束重新编号}：水电约束为25-33号，与其他发电约束统一管理
   \end{itemize}

\item \textbf{核电建模方式调整}：
   \begin{itemize}
   \item \textbf{移除启停建模}：取消核电的启停变量和约束，回归简化建模
   \item \textbf{月度容量系数约束}：核电按月度容量系数建模：$p_{n,t}^{nuclear} = \alpha_{n,m}^{nu} \cdot I_{n}^{nuclear,0}$
   \item \textbf{参数简化}：移除核电的燃料消耗成本和启停成本参数
   \end{itemize}

\item \textbf{运行成本结构完善}：
   \begin{itemize}
   \item \textbf{新增运行边际成本参数}：$MC_{n}^{hydro\_res}$、$MC_{n}^{hydro\_ror}$、$MC_{n}^{geo}$、$MC_{n}^{nuclear}$
   \item \textbf{目标函数公式1f}：增加水电、地热、核电的运行成本项
   \item \textbf{成本建模完整性}：所有发电技术都包含适当的运行成本
   \end{itemize}

\item \textbf{变量定义清理}：
   \begin{itemize}
   \item \textbf{移除核电启停变量}：删除$\bar{p}_{n,t}^{nuclear}$、$x_{n,t}^{nuclear,su}$、$x_{n,t}^{nuclear,sd}$
   \item \textbf{变量非负性约束更新}：移除核电启停变量的非负性约束
   \end{itemize}

\item \textbf{约束体系重构}：
   \begin{itemize}
   \item \textbf{核电约束简化}：核电只保留月度容量系数约束和扩张限制约束
   \item \textbf{水电约束合并}：水电约束纳入发电约束统一管理
   \item \textbf{全部约束重新编号}：从2-46号，提高模型结构清晰度
   \end{itemize}
\end{enumerate}

\textbf{建模技术特点更新：}
\begin{itemize}
\item \textbf{核电简化建模}：核电回归月度容量系数约束，避免过度复杂化
\item \textbf{运行成本完整性}：所有发电技术都具备相应的运行成本建模
\item \textbf{约束结构优化}：水电约束合并，编号统一，提高模型可读性
\item \textbf{参数体系完善}：新增运行边际成本参数，支持多种发电技术运行成本计算
\end{itemize}

\textbf{模型增强点：}
\begin{itemize}
\item \textbf{建模简洁性}：核电建模回归简化，减少不必要的复杂性
\item \textbf{成本建模精细化}：区分燃料消耗成本和运行边际成本，提高经济分析精度
\item \textbf{约束管理优化}：约束条件统一编号和组织，提高模型维护性
\item \textbf{参数化合理性}：核电参数简化，水电等增加运行成本，更符合实际情况
\end{itemize}

\subsection{2025年08月11日 储能和水电建模简化修订（第八次更新）}

\textbf{主要修改内容：}

\begin{enumerate}
\item \textbf{储能技术建模简化}：基于储能数据处理方法学的E/P比例分析结果进行重大简化：
   \begin{itemize}
   \item \textbf{固定E/P比例}：电池储能(BAT)采用E/P=2.12小时，抽水蓄能(PSH)采用E/P=38.18小时
   \item \textbf{删除能量容量投资变量}：$I_{n}^{z,E}$ - 储能能量容量通过功率容量×E/P比例自动计算
   \item \textbf{删除能量投资成本参数}：$IC_{n}^{z,E}$ - 仅保留功率投资成本$IC_{n}^{z,P}$
   \item \textbf{删除能量运维成本参数}：$FOM_{n}^{z,E}$ - 仅保留功率运维成本$FOM_{n}^{z,P}$
   \item \textbf{删除自放电参数}：$\sigma_{z}$ - 简化储能能量平衡约束
   \item \textbf{能量水平约束简化}：基于功率容量和固定E/P比例计算能量上限
   \end{itemize}

\item \textbf{水电建模简化}：将大坝式水电从站点级别改为国家级别统一建模：
   \begin{itemize}
   \item \textbf{删除水电站集合}：$H_{n}$ - 国家水电站集合不再需要
   \item \textbf{变量简化}：$I_{n}^{hydro\_res}$ - 按国家统一的水库式水电装机容量
   \item \textbf{运行变量统一}：$p_{n,t}^{res}$、$q_{n,t}^{out}$、$v_{n,t}$ - 按国家级别建模
   \item \textbf{参数统一}：$IC_{n}^{hydro\_res}$、$FOM_{n}^{hydro\_res}$、$MC_{n}^{hydro\_res}$ - 按国家统一参数
   \item \textbf{水资源平衡简化}：$Q_{n,t}^{in}$、$V_{n}^{max}$、$V_{n}^{min}$ - 国家总体水资源平衡
   \item \textbf{约束条件简化}：所有水电约束从站点级别$(n,h)$简化为国家级别$(n)$
   \end{itemize}

\item \textbf{目标函数更新}：
   \begin{itemize}
   \item \textbf{储能投资成本}：$\sum_{n \in N} \sum_{z \in Z} IC_{n}^{z,P} \cdot I_{n}^{z,P}$ - 仅功率投资成本
   \item \textbf{储能运维成本}：$\sum_{n \in N} \sum_{z \in Z} FOM_{n}^{z,P} \cdot (I_{n}^{z,P,0} + I_{n}^{z,P})$ - 仅功率运维成本
   \item \textbf{水电投资成本}：$IC_{n}^{hydro\_res} \cdot I_{n}^{hydro\_res}$ - 按国家统一计算
   \item \textbf{水电运维成本}：$FOM_{n}^{hydro\_res} \cdot (I_{n}^{hydro\_res,0} + I_{n}^{hydro\_res})$ - 按国家统一计算
   \item \textbf{水电运行成本}：$MC_{n}^{hydro\_res} \cdot p_{n,t}^{res}$ - 按国家统一计算
   \end{itemize}

\item \textbf{约束条件更新}：
   \begin{itemize}
   \item \textbf{储能能量约束}：$\underline{v}_{es}^{z} \cdot (I_{n}^{z,P,0} + I_{n}^{z,P}) \cdot E2P_z \leq e_{n,t}^{z} \leq \overline{v}_{es}^{z} \cdot (I_{n}^{z,P,0} + I_{n}^{z,P}) \cdot E2P_z$
   \item \textbf{水电装机约束}：$I_{n}^{hydro\_res,0} + I_{n}^{hydro\_res} \leq I_{n}^{hydro\_res,p}$ - 按国家统一约束
   \item \textbf{水库平衡约束}：$v_{n,t} = v_{n,t-1} + Q_{n,t}^{in} - q_{n,t}^{out} - q_{n,t}^{spill}$ - 国家总体平衡
   \item \textbf{供需平衡约束}：$p_{n,t}^{res}$ - 水电项简化为国家级别
   \end{itemize}

\item \textbf{变量非负性约束更新}：
   \begin{itemize}
   \item \textbf{删除储能能量容量变量}：$I_{n}^{z,E}$ 不再出现在非负性约束中
   \item \textbf{水电变量简化}：$I_{n}^{hydro\_res}$、$p_{n,t}^{res}$、$q_{n,t}^{out}$、$v_{n,t}$ - 按国家级别约束
   \end{itemize}
\end{enumerate}

\textbf{建模技术特点更新：}
\begin{itemize}
\item \textbf{储能建模简化}：采用固定E/P比例，大幅简化储能系统建模复杂度
\item \textbf{水电建模统一}：水库式水电从站点级别简化为国家级别，提高计算效率
\item \textbf{参数化精简}：删除不必要的参数和变量，保持模型核心功能
\item \textbf{约束体系优化}：简化约束条件，减少模型规模和求解复杂度
\end{itemize}

\textbf{模型增强点：}
\begin{itemize}
\item \textbf{计算效率提升}：储能和水电建模简化显著减少变量和约束数量
\item \textbf{数据驱动参数化}：储能E/P比例基于GESDB全球储能数据库分析结果
\item \textbf{建模现实性保持}：简化的同时保持储能和水电的核心技术特征
\item \textbf{求解稳定性增强}：减少模型复杂度，提高大规模优化问题的求解稳定性
\end{itemize}

\textbf{技术创新点：}
\begin{itemize}
\item \textbf{储能E/P比例固定化}：首次在大规模电力系统规划中采用固定E/P比例简化储能建模
\item \textbf{水电国家级别统一}：将复杂的站点级别水电建模简化为国家级别统一建模
\item \textbf{参数精简策略}：在保持模型核心功能的前提下，最大化简化参数体系
\item \textbf{约束优化设计}：通过约束条件重构，实现建模简化与功能完整性的平衡
\end{itemize}

\end{CJK}
\end{document}